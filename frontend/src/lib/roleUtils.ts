// Role detection and management utilities for Trodoo
import type { User } from '../types/user.ts';
import { getUserVenues, getUserBookings } from './pocketbase.ts';

export type UserRole = 'renter' | 'owner' | 'admin';

export interface UserRoleInfo {
  roles: UserRole[];
  isRenter: boolean;
  isOwner: boolean;
  isAdmin: boolean;
  isDualRole: boolean;
  primaryRole: UserRole;
  hasVenues: boolean;
  venueCount: number;
  hasBookingsAsRenter: boolean;
  hasBookingsAsOwner: boolean;
}

/**
 * Comprehensive role detection for a user
 * Combines explicit roles from user.roles with implicit ownership detection
 */
export async function detectUserRoles(user: User): Promise<UserRoleInfo> {
  if (!user) {
    return {
      roles: [],
      isRenter: false,
      isOwner: false,
      isAdmin: false,
      isDualRole: false,
      primaryRole: 'renter',
      hasVenues: false,
      venueCount: 0,
      hasBookingsAsRenter: false,
      hasBookingsAsOwner: false,
    };
  }

  // Get explicit roles from user record
  const explicitRoles = user.roles || ['renter'];
  
  // Check for admin role
  const isAdmin = explicitRoles.includes('admin');
  
  // Check for explicit owner role
  const hasExplicitOwnerRole = explicitRoles.includes('owner');
  
  // Check for explicit renter role
  const hasExplicitRenterRole = explicitRoles.includes('renter');

  try {
    // Check if user owns any venues (implicit ownership)
    const venuesResult = await getUserVenues(user.id, 1, 1);
    const hasVenues = venuesResult.success && venuesResult.totalItems > 0;
    const venueCount = venuesResult.success ? venuesResult.totalItems : 0;

    // Check booking history to determine activity patterns
    const [renterBookingsResult, ownerBookingsResult] = await Promise.all([
      getUserBookings(user.id, 1, 1, 'renter'),
      getUserBookings(user.id, 1, 1, 'owner')
    ]);

    const hasBookingsAsRenter = renterBookingsResult.success && renterBookingsResult.totalItems > 0;
    const hasBookingsAsOwner = ownerBookingsResult.success && ownerBookingsResult.totalItems > 0;

    // Determine actual roles (explicit + implicit)
    const actualRoles: UserRole[] = [];
    
    // Admin role
    if (isAdmin) {
      actualRoles.push('admin');
    }
    
    // Owner role (explicit or implicit through venue ownership)
    const isOwner = hasExplicitOwnerRole || hasVenues;
    if (isOwner) {
      actualRoles.push('owner');
    }
    
    // Renter role (explicit or implicit through booking activity)
    const isRenter = hasExplicitRenterRole || hasBookingsAsRenter || actualRoles.length === 0;
    if (isRenter) {
      actualRoles.push('renter');
    }

    // Determine primary role based on activity and explicit preferences
    let primaryRole: UserRole = 'renter';
    
    if (isAdmin) {
      primaryRole = 'admin';
    } else if (isOwner && hasVenues) {
      // If user has venues, they're primarily an owner
      primaryRole = 'owner';
    } else if (isOwner && hasBookingsAsOwner && !hasBookingsAsRenter) {
      // If user only has owner bookings, they're primarily an owner
      primaryRole = 'owner';
    } else if (hasBookingsAsRenter || hasExplicitRenterRole) {
      primaryRole = 'renter';
    }

    const isDualRole = actualRoles.length > 1 && !isAdmin;

    return {
      roles: actualRoles,
      isRenter,
      isOwner,
      isAdmin,
      isDualRole,
      primaryRole,
      hasVenues,
      venueCount,
      hasBookingsAsRenter,
      hasBookingsAsOwner,
    };
  } catch (error) {
    console.error('Error detecting user roles:', error);
    
    // Fallback to explicit roles only
    return {
      roles: explicitRoles as UserRole[],
      isRenter: hasExplicitRenterRole,
      isOwner: hasExplicitOwnerRole,
      isAdmin,
      isDualRole: explicitRoles.length > 1,
      primaryRole: isAdmin ? 'admin' : (hasExplicitOwnerRole ? 'owner' : 'renter'),
      hasVenues: false,
      venueCount: 0,
      hasBookingsAsRenter: false,
      hasBookingsAsOwner: false,
    };
  }
}

/**
 * Quick role check without async operations
 * Uses only explicit roles from user record
 */
export function getExplicitUserRoles(user: User | null): UserRoleInfo {
  if (!user) {
    return {
      roles: [],
      isRenter: false,
      isOwner: false,
      isAdmin: false,
      isDualRole: false,
      primaryRole: 'renter',
      hasVenues: false,
      venueCount: 0,
      hasBookingsAsRenter: false,
      hasBookingsAsOwner: false,
    };
  }

  const explicitRoles = user.roles || ['renter'];
  const isAdmin = explicitRoles.includes('admin');
  const isOwner = explicitRoles.includes('owner');
  const isRenter = explicitRoles.includes('renter');
  
  const primaryRole: UserRole = isAdmin ? 'admin' : (isOwner ? 'owner' : 'renter');
  const isDualRole = explicitRoles.length > 1 && !isAdmin;

  return {
    roles: explicitRoles as UserRole[],
    isRenter,
    isOwner,
    isAdmin,
    isDualRole,
    primaryRole,
    hasVenues: false, // Unknown without async check
    venueCount: 0,
    hasBookingsAsRenter: false,
    hasBookingsAsOwner: false,
  };
}

/**
 * Check if user has a specific role
 */
export function hasRole(user: User | null, role: UserRole): boolean {
  if (!user) return false;
  return user.roles?.includes(role) || false;
}

/**
 * Check if user can access owner features
 * (has owner role OR owns venues)
 */
export async function canAccessOwnerFeatures(user: User): Promise<boolean> {
  if (!user) return false;
  
  // Check explicit owner role first
  if (hasRole(user, 'owner')) return true;
  
  // Check if user owns any venues
  try {
    const venuesResult = await getUserVenues(user.id, 1, 1);
    return venuesResult.success && venuesResult.totalItems > 0;
  } catch (error) {
    console.error('Error checking venue ownership:', error);
    return false;
  }
}

/**
 * Check if user can access admin features
 */
export function canAccessAdminFeatures(user: User | null): boolean {
  return hasRole(user, 'admin');
}

/**
 * Get dashboard features that should be visible for user's roles
 */
export function getDashboardFeatures(roleInfo: UserRoleInfo): {
  showRenterFeatures: boolean;
  showOwnerFeatures: boolean;
  showAdminFeatures: boolean;
  showRoleSwitcher: boolean;
  defaultView: 'renter' | 'owner' | 'admin' | 'combined';
} {
  const { isRenter, isOwner, isAdmin, isDualRole, primaryRole } = roleInfo;
  
  return {
    showRenterFeatures: isRenter,
    showOwnerFeatures: isOwner,
    showAdminFeatures: isAdmin,
    showRoleSwitcher: isDualRole,
    defaultView: isAdmin ? 'admin' : (isDualRole ? 'combined' : primaryRole),
  };
}

/**
 * Get navigation items based on user roles
 */
export function getNavigationItems(roleInfo: UserRoleInfo): Array<{
  label: string;
  href: string;
  icon: string;
  roles: UserRole[];
}> {
  const items: Array<{
    label: string;
    href: string;
    icon: string;
    roles: UserRole[];
  }> = [];

  // Renter navigation items
  if (roleInfo.isRenter) {
    items.push(
      { label: 'Browse Venues', href: '/venues', icon: 'search', roles: ['renter'] as UserRole[] },
      { label: 'My Bookings', href: '/bookings', icon: 'calendar', roles: ['renter'] as UserRole[] },
      { label: 'Favorites', href: '/favorites', icon: 'heart', roles: ['renter'] as UserRole[] }
    );
  }

  // Owner navigation items
  if (roleInfo.isOwner) {
    items.push(
      { label: 'My Venues', href: '/dashboard/my-venues', icon: 'building', roles: ['owner'] as UserRole[] },
      { label: 'Booking Requests', href: '/dashboard/bookings', icon: 'inbox', roles: ['owner'] as UserRole[] },
      { label: 'Analytics', href: '/dashboard/analytics', icon: 'chart', roles: ['owner'] as UserRole[] }
    );
  }

  // Admin navigation items
  if (roleInfo.isAdmin) {
    items.push(
      { label: 'Admin Dashboard', href: '/admin', icon: 'shield', roles: ['admin'] as UserRole[] },
      { label: 'User Management', href: '/admin/users', icon: 'users', roles: ['admin'] as UserRole[] },
      { label: 'Content Moderation', href: '/admin/flagged-content', icon: 'flag', roles: ['admin'] as UserRole[] }
    );
  }

  return items;
}
