// Test file for role detection utilities
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { 
  getExplicitUserRoles, 
  hasRole, 
  getDashboardFeatures, 
  getNavigationItems,
  canAccessAdminFeatures 
} from '../roleUtils.ts';
import type { User } from '../../types/user.ts';

// Mock PocketBase functions
vi.mock('../pocketbase.ts', () => ({
  getUserVenues: vi.fn(),
  getUserBookings: vi.fn(),
}));

describe('Role Detection Utils', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('getExplicitUserRoles', () => {
    it('should return default renter role for user with no roles', () => {
      const user: User = {
        id: '1',
        email: '<EMAIL>',
        created: '2023-01-01',
        updated: '2023-01-01',
      };

      const result = getExplicitUserRoles(user);
      
      expect(result.roles).toEqual(['renter']);
      expect(result.isRenter).toBe(true);
      expect(result.isOwner).toBe(false);
      expect(result.isAdmin).toBe(false);
      expect(result.primaryRole).toBe('renter');
    });

    it('should detect admin role correctly', () => {
      const user: User = {
        id: '1',
        email: '<EMAIL>',
        roles: ['admin', 'renter'],
        created: '2023-01-01',
        updated: '2023-01-01',
      };

      const result = getExplicitUserRoles(user);
      
      expect(result.roles).toEqual(['admin', 'renter']);
      expect(result.isAdmin).toBe(true);
      expect(result.isRenter).toBe(true);
      expect(result.primaryRole).toBe('admin');
    });

    it('should detect dual-role user (owner + renter)', () => {
      const user: User = {
        id: '1',
        email: '<EMAIL>',
        roles: ['owner', 'renter'],
        created: '2023-01-01',
        updated: '2023-01-01',
      };

      const result = getExplicitUserRoles(user);
      
      expect(result.roles).toEqual(['owner', 'renter']);
      expect(result.isOwner).toBe(true);
      expect(result.isRenter).toBe(true);
      expect(result.isDualRole).toBe(true);
      expect(result.primaryRole).toBe('owner');
    });

    it('should return empty roles for null user', () => {
      const result = getExplicitUserRoles(null);
      
      expect(result.roles).toEqual([]);
      expect(result.isRenter).toBe(false);
      expect(result.isOwner).toBe(false);
      expect(result.isAdmin).toBe(false);
    });
  });

  describe('hasRole', () => {
    it('should return true when user has the specified role', () => {
      const user: User = {
        id: '1',
        email: '<EMAIL>',
        roles: ['owner', 'renter'],
        created: '2023-01-01',
        updated: '2023-01-01',
      };

      expect(hasRole(user, 'owner')).toBe(true);
      expect(hasRole(user, 'renter')).toBe(true);
      expect(hasRole(user, 'admin')).toBe(false);
    });

    it('should return false for null user', () => {
      expect(hasRole(null, 'admin')).toBe(false);
    });
  });

  describe('canAccessAdminFeatures', () => {
    it('should return true for admin users', () => {
      const adminUser: User = {
        id: '1',
        email: '<EMAIL>',
        roles: ['admin'],
        created: '2023-01-01',
        updated: '2023-01-01',
      };

      expect(canAccessAdminFeatures(adminUser)).toBe(true);
    });

    it('should return false for non-admin users', () => {
      const regularUser: User = {
        id: '1',
        email: '<EMAIL>',
        roles: ['renter'],
        created: '2023-01-01',
        updated: '2023-01-01',
      };

      expect(canAccessAdminFeatures(regularUser)).toBe(false);
    });
  });

  describe('getDashboardFeatures', () => {
    it('should show correct features for renter', () => {
      const roleInfo = {
        roles: ['renter'],
        isRenter: true,
        isOwner: false,
        isAdmin: false,
        isDualRole: false,
        primaryRole: 'renter' as const,
        hasVenues: false,
        venueCount: 0,
        hasBookingsAsRenter: true,
        hasBookingsAsOwner: false,
      };

      const features = getDashboardFeatures(roleInfo);
      
      expect(features.showRenterFeatures).toBe(true);
      expect(features.showOwnerFeatures).toBe(false);
      expect(features.showAdminFeatures).toBe(false);
      expect(features.showRoleSwitcher).toBe(false);
      expect(features.defaultView).toBe('renter');
    });

    it('should show correct features for dual-role user', () => {
      const roleInfo = {
        roles: ['owner', 'renter'],
        isRenter: true,
        isOwner: true,
        isAdmin: false,
        isDualRole: true,
        primaryRole: 'owner' as const,
        hasVenues: true,
        venueCount: 2,
        hasBookingsAsRenter: true,
        hasBookingsAsOwner: true,
      };

      const features = getDashboardFeatures(roleInfo);
      
      expect(features.showRenterFeatures).toBe(true);
      expect(features.showOwnerFeatures).toBe(true);
      expect(features.showAdminFeatures).toBe(false);
      expect(features.showRoleSwitcher).toBe(true);
      expect(features.defaultView).toBe('combined');
    });

    it('should show correct features for admin', () => {
      const roleInfo = {
        roles: ['admin'],
        isRenter: false,
        isOwner: false,
        isAdmin: true,
        isDualRole: false,
        primaryRole: 'admin' as const,
        hasVenues: false,
        venueCount: 0,
        hasBookingsAsRenter: false,
        hasBookingsAsOwner: false,
      };

      const features = getDashboardFeatures(roleInfo);
      
      expect(features.showRenterFeatures).toBe(false);
      expect(features.showOwnerFeatures).toBe(false);
      expect(features.showAdminFeatures).toBe(true);
      expect(features.showRoleSwitcher).toBe(false);
      expect(features.defaultView).toBe('admin');
    });
  });

  describe('getNavigationItems', () => {
    it('should return renter navigation items', () => {
      const roleInfo = {
        roles: ['renter'],
        isRenter: true,
        isOwner: false,
        isAdmin: false,
        isDualRole: false,
        primaryRole: 'renter' as const,
        hasVenues: false,
        venueCount: 0,
        hasBookingsAsRenter: true,
        hasBookingsAsOwner: false,
      };

      const items = getNavigationItems(roleInfo);
      
      expect(items).toHaveLength(3);
      expect(items.map(item => item.label)).toEqual([
        'Browse Venues',
        'My Bookings', 
        'Favorites'
      ]);
    });

    it('should return owner navigation items', () => {
      const roleInfo = {
        roles: ['owner'],
        isRenter: false,
        isOwner: true,
        isAdmin: false,
        isDualRole: false,
        primaryRole: 'owner' as const,
        hasVenues: true,
        venueCount: 2,
        hasBookingsAsRenter: false,
        hasBookingsAsOwner: true,
      };

      const items = getNavigationItems(roleInfo);
      
      expect(items).toHaveLength(3);
      expect(items.map(item => item.label)).toEqual([
        'My Venues',
        'Booking Requests',
        'Analytics'
      ]);
    });

    it('should return combined navigation for dual-role user', () => {
      const roleInfo = {
        roles: ['owner', 'renter'],
        isRenter: true,
        isOwner: true,
        isAdmin: false,
        isDualRole: true,
        primaryRole: 'owner' as const,
        hasVenues: true,
        venueCount: 1,
        hasBookingsAsRenter: true,
        hasBookingsAsOwner: true,
      };

      const items = getNavigationItems(roleInfo);
      
      expect(items).toHaveLength(6); // 3 renter + 3 owner items
      expect(items.map(item => item.label)).toEqual([
        'Browse Venues',
        'My Bookings',
        'Favorites',
        'My Venues',
        'Booking Requests',
        'Analytics'
      ]);
    });

    it('should return admin navigation items', () => {
      const roleInfo = {
        roles: ['admin'],
        isRenter: false,
        isOwner: false,
        isAdmin: true,
        isDualRole: false,
        primaryRole: 'admin' as const,
        hasVenues: false,
        venueCount: 0,
        hasBookingsAsRenter: false,
        hasBookingsAsOwner: false,
      };

      const items = getNavigationItems(roleInfo);
      
      expect(items).toHaveLength(3);
      expect(items.map(item => item.label)).toEqual([
        'Admin Dashboard',
        'User Management',
        'Content Moderation'
      ]);
    });
  });
});
