import { useState, useEffect } from 'react';
import { useStore } from '@nanostores/react';
import { userStore } from '../../lib/state.ts';
import { getUserBookings, getUserVenues } from '../../lib/pocketbase.ts';
import type { Booking } from '../../types/booking.ts';
import BookingList from './BookingList.tsx';
import { Building, Calendar, DollarSign, TrendingUp, Plus, BarChart3, Users, Clock } from 'lucide-react';

interface OwnerDashboardProps {
  className?: string;
}

interface OwnerStats {
  totalVenues: number;
  activeVenues: number;
  totalBookings: number;
  pendingRequests: number;
  totalEarnings: number;
  monthlyEarnings: number;
  averageRating: number;
  occupancyRate: number;
}

export default function OwnerDashboard({ className = '' }: OwnerDashboardProps) {
  const user = useStore(userStore);
  const [bookings, setBookings] = useState<Booking[]>([]);
  const [venues, setVenues] = useState<any[]>([]);
  const [stats, setStats] = useState<OwnerStats>({
    totalVenues: 0,
    activeVenues: 0,
    totalBookings: 0,
    pendingRequests: 0,
    totalEarnings: 0,
    monthlyEarnings: 0,
    averageRating: 0,
    occupancyRate: 0,
  });
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (user) {
      loadOwnerData();
    }
  }, [user]);

  const loadOwnerData = async () => {
    if (!user) return;

    try {
      setIsLoading(true);
      setError(null);

      // Load owner bookings and venues in parallel
      const [bookingsResult, venuesResult] = await Promise.all([
        getUserBookings(user.id, 1, 20, 'owner'),
        getUserVenues(user.id, 1, 50)
      ]);
      
      if (bookingsResult.success) {
        setBookings(bookingsResult.bookings);
      }

      if (venuesResult.success) {
        setVenues(venuesResult.venues);
      }

      // Calculate stats
      const totalVenues = venuesResult.success ? venuesResult.totalItems : 0;
      const activeVenues = venuesResult.success 
        ? venuesResult.venues.filter((v: any) => v.is_published).length 
        : 0;

      const totalBookings = bookingsResult.success ? bookingsResult.totalItems : 0;
      const pendingRequests = bookingsResult.success 
        ? bookingsResult.bookings.filter(booking => booking.status === 'pending').length
        : 0;

      const totalEarnings = bookingsResult.success
        ? bookingsResult.bookings
            .filter(booking => ['completed', 'paid'].includes(booking.status))
            .reduce((sum, booking) => sum + (booking.payout_amount || 0), 0)
        : 0;

      // Calculate monthly earnings (current month)
      const currentMonth = new Date().getMonth();
      const currentYear = new Date().getFullYear();
      const monthlyEarnings = bookingsResult.success
        ? bookingsResult.bookings
            .filter(booking => {
              const bookingDate = new Date(booking.created);
              return bookingDate.getMonth() === currentMonth && 
                     bookingDate.getFullYear() === currentYear &&
                     ['completed', 'paid'].includes(booking.status);
            })
            .reduce((sum, booking) => sum + (booking.payout_amount || 0), 0)
        : 0;

      // Calculate average rating across all venues
      const venuesWithRatings = venuesResult.success 
        ? venuesResult.venues.filter((v: any) => v.average_rating > 0)
        : [];
      const averageRating = venuesWithRatings.length > 0
        ? venuesWithRatings.reduce((sum: number, v: any) => sum + v.average_rating, 0) / venuesWithRatings.length
        : 0;

      setStats({
        totalVenues,
        activeVenues,
        totalBookings,
        pendingRequests,
        totalEarnings,
        monthlyEarnings,
        averageRating,
        occupancyRate: 0, // TODO: Calculate based on booking frequency
      });

      if (!bookingsResult.success && !venuesResult.success) {
        setError('Failed to load dashboard data');
      }
    } catch (err) {
      console.error('Error loading owner data:', err);
      setError('Failed to load dashboard data');
    } finally {
      setIsLoading(false);
    }
  };

  const handleBookingAction = (bookingId: string, action: string) => {
    switch (action) {
      case 'view':
      case 'message':
        window.location.href = `/bookings/${bookingId}`;
        break;
      case 'approve':
        // TODO: Implement booking approval
        window.location.href = `/bookings/${bookingId}?action=approve`;
        break;
      case 'decline':
        // TODO: Implement booking decline
        window.location.href = `/bookings/${bookingId}?action=decline`;
        break;
      default:
        window.location.href = `/bookings/${bookingId}`;
    }
  };

  if (isLoading) {
    return (
      <div className={`animate-pulse ${className}`}>
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          {[...Array(4)].map((_, i) => (
            <div key={i} className="bg-white rounded-xl shadow-sm border border-slate-200 p-6">
              <div className="h-4 bg-slate-200 rounded w-3/4 mb-2"></div>
              <div className="h-8 bg-slate-200 rounded w-1/2"></div>
            </div>
          ))}
        </div>
        <div className="bg-white rounded-xl shadow-sm border border-slate-200 p-6">
          <div className="h-6 bg-slate-200 rounded w-1/4 mb-4"></div>
          <div className="space-y-3">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="h-16 bg-slate-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`bg-red-50 border border-red-200 rounded-xl p-6 ${className}`}>
        <div className="flex items-center">
          <div className="text-red-500 mr-3">
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <div>
            <h3 className="text-red-800 font-medium">Error Loading Dashboard</h3>
            <p className="text-red-600 text-sm mt-1">{error}</p>
          </div>
        </div>
        <button
          onClick={loadOwnerData}
          className="mt-4 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
        >
          Try Again
        </button>
      </div>
    );
  }

  return (
    <div className={className}>
      {/* Welcome Section */}
      <div className="mb-8">
        <h2 className="text-2xl font-bold text-slate-900 mb-2">
          Welcome back, {user?.name || 'Venue Owner'}!
        </h2>
        <p className="text-slate-600">
          Manage your venues, track bookings, and grow your business.
        </p>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <div className="bg-white rounded-xl shadow-sm border border-slate-200 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-slate-600">Total Venues</p>
              <p className="text-2xl font-bold text-slate-900">{stats.totalVenues}</p>
              <p className="text-xs text-green-600 mt-1">{stats.activeVenues} active</p>
            </div>
            <div className="p-3 bg-blue-100 rounded-lg">
              <Building className="w-6 h-6 text-blue-600" />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-sm border border-slate-200 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-slate-600">Pending Requests</p>
              <p className="text-2xl font-bold text-slate-900">{stats.pendingRequests}</p>
              <p className="text-xs text-slate-500 mt-1">Awaiting response</p>
            </div>
            <div className="p-3 bg-orange-100 rounded-lg">
              <Clock className="w-6 h-6 text-orange-600" />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-sm border border-slate-200 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-slate-600">Monthly Earnings</p>
              <p className="text-2xl font-bold text-slate-900">
                ₦{stats.monthlyEarnings.toLocaleString()}
              </p>
              <p className="text-xs text-slate-500 mt-1">This month</p>
            </div>
            <div className="p-3 bg-green-100 rounded-lg">
              <DollarSign className="w-6 h-6 text-green-600" />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-sm border border-slate-200 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-slate-600">Average Rating</p>
              <p className="text-2xl font-bold text-slate-900">
                {stats.averageRating > 0 ? stats.averageRating.toFixed(1) : '—'}
              </p>
              <p className="text-xs text-slate-500 mt-1">Across all venues</p>
            </div>
            <div className="p-3 bg-purple-100 rounded-lg">
              <TrendingUp className="w-6 h-6 text-purple-600" />
            </div>
          </div>
        </div>
      </div>

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Recent Booking Requests */}
        <div className="lg:col-span-2">
          <div className="bg-white rounded-xl shadow-sm border border-slate-200">
            <div className="px-6 py-4 border-b border-slate-200">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold text-slate-900">Recent Booking Requests</h3>
                <a
                  href="/dashboard/bookings"
                  className="text-sm text-primary-600 hover:text-primary-700 font-medium"
                >
                  View All
                </a>
              </div>
            </div>
            <div className="p-6">
              {bookings.length > 0 ? (
                <BookingList
                  bookings={bookings.slice(0, 5)}
                  userRole="owner"
                  onBookingAction={handleBookingAction}
                  isLoading={false}
                />
              ) : (
                <div className="text-center py-8">
                  <Calendar className="w-12 h-12 text-slate-300 mx-auto mb-4" />
                  <h4 className="text-lg font-medium text-slate-900 mb-2">No booking requests yet</h4>
                  <p className="text-slate-600 mb-4">
                    {stats.totalVenues === 0 
                      ? "Start by listing your first venue to receive bookings."
                      : "Your venues are ready to receive booking requests."
                    }
                  </p>
                  {stats.totalVenues === 0 && (
                    <a
                      href="/venues/new"
                      className="inline-flex items-center px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors"
                    >
                      <Plus className="w-4 h-4 mr-2" />
                      List Your First Venue
                    </a>
                  )}
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Quick Actions & Performance */}
        <div className="space-y-6">
          {/* Quick Actions */}
          <div className="bg-white rounded-xl shadow-sm border border-slate-200">
            <div className="px-6 py-4 border-b border-slate-200">
              <h3 className="text-lg font-semibold text-slate-900">Quick Actions</h3>
            </div>
            <div className="p-6 space-y-3">
              <a
                href="/venues/new"
                className="flex items-center p-3 border border-slate-200 rounded-lg hover:bg-slate-50 transition-colors group"
              >
                <div className="p-2 bg-primary-100 rounded-lg group-hover:bg-primary-200 transition-colors">
                  <Plus className="w-5 h-5 text-primary-600" />
                </div>
                <div className="ml-3">
                  <h4 className="text-sm font-medium text-slate-900">Add New Venue</h4>
                  <p className="text-xs text-slate-600">List a new space</p>
                </div>
              </a>

              <a
                href="/dashboard/my-venues"
                className="flex items-center p-3 border border-slate-200 rounded-lg hover:bg-slate-50 transition-colors group"
              >
                <div className="p-2 bg-blue-100 rounded-lg group-hover:bg-blue-200 transition-colors">
                  <Building className="w-5 h-5 text-blue-600" />
                </div>
                <div className="ml-3">
                  <h4 className="text-sm font-medium text-slate-900">Manage Venues</h4>
                  <p className="text-xs text-slate-600">Edit your listings</p>
                </div>
              </a>

              <a
                href="/dashboard/analytics"
                className="flex items-center p-3 border border-slate-200 rounded-lg hover:bg-slate-50 transition-colors group"
              >
                <div className="p-2 bg-green-100 rounded-lg group-hover:bg-green-200 transition-colors">
                  <BarChart3 className="w-5 h-5 text-green-600" />
                </div>
                <div className="ml-3">
                  <h4 className="text-sm font-medium text-slate-900">View Analytics</h4>
                  <p className="text-xs text-slate-600">Performance insights</p>
                </div>
              </a>
            </div>
          </div>

          {/* Performance Summary */}
          <div className="bg-gradient-to-br from-green-50 to-green-100 rounded-xl p-6 border border-green-200">
            <div className="flex items-center mb-3">
              <TrendingUp className="w-5 h-5 text-green-600 mr-2" />
              <h3 className="text-lg font-semibold text-green-900">Performance</h3>
            </div>
            <div className="space-y-3">
              <div className="flex justify-between items-center">
                <span className="text-green-700 text-sm">Total Earnings</span>
                <span className="text-green-900 font-semibold">
                  ₦{stats.totalEarnings.toLocaleString()}
                </span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-green-700 text-sm">Total Bookings</span>
                <span className="text-green-900 font-semibold">{stats.totalBookings}</span>
              </div>
              {stats.averageRating > 0 && (
                <div className="flex justify-between items-center">
                  <span className="text-green-700 text-sm">Avg. Rating</span>
                  <span className="text-green-900 font-semibold">
                    {stats.averageRating.toFixed(1)} ⭐
                  </span>
                </div>
              )}
            </div>
            <a
              href="/dashboard/analytics"
              className="inline-flex items-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors text-sm font-medium mt-4"
            >
              View Detailed Analytics
            </a>
          </div>
        </div>
      </div>
    </div>
  );
}
